-- =====================================================
-- EDUCATIONAL FORUM DATABASE SCHEMA FOR SQL SERVER
-- Similar to Vietjack platform
-- =====================================================

-- Use the database
USE [DoAnChuyenNganh];
GO

-- =====================================================
-- 1. USER MANAGEMENT TABLES
-- =====================================================

-- Core user accounts table
CREATE TABLE users (
    id INT IDENTITY(1,1) PRIMARY KEY,
    uuid UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    username NVARCHAR(50) NOT NULL,
    email NVARCHAR(255) NOT NULL,
    password_hash NVARCHAR(255) NOT NULL,
    first_name NVARCHAR(100),
    last_name NVARCHAR(100),
    avatar_url NVARCHAR(500),
    bio NTEXT,
    phone NVARCHAR(20),
    date_of_birth DATE,
    gender NVARCHAR(10) CHECK (gender IN ('male', 'female', 'other')),
    role NVARCHAR(20) CHECK (role IN ('student', 'instructor', 'admin')) DEFAULT 'student',
    account_status NVARCHAR(20) CHECK (account_status IN ('active', 'locked', 'suspended', 'pending')) DEFAULT 'active',
    email_verified BIT DEFAULT 0,
    email_verification_token NVARCHAR(255),
    password_reset_token NVARCHAR(255),
    password_reset_expires DATETIME2,
    last_login DATETIME2,
    login_count INT DEFAULT 0,
    experience_points INT DEFAULT 0,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT UQ_users_uuid UNIQUE (uuid),
    CONSTRAINT UQ_users_username UNIQUE (username),
    CONSTRAINT UQ_users_email UNIQUE (email)
);

-- Create indexes for users table
CREATE INDEX IX_users_username ON users(username);
CREATE INDEX IX_users_email ON users(email);
CREATE INDEX IX_users_role ON users(role);
CREATE INDEX IX_users_status ON users(account_status);
CREATE INDEX IX_users_experience ON users(experience_points DESC);

-- User profiles with extended information
CREATE TABLE user_profiles (
    id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    school_name NVARCHAR(200),
    grade_level NVARCHAR(20),
    city NVARCHAR(100),
    province NVARCHAR(100),
    country NVARCHAR(100) DEFAULT N'Vietnam',
    preferred_language NVARCHAR(10) DEFAULT 'vi',
    timezone NVARCHAR(50) DEFAULT 'Asia/Ho_Chi_Minh',
    learning_goals NTEXT,
    interests NVARCHAR(MAX), -- Store as JSON string
    social_links NVARCHAR(MAX), -- Facebook, Instagram, etc.
    privacy_settings NVARCHAR(MAX), -- Privacy preferences
    notification_preferences NVARCHAR(MAX),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_user_profiles_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for user_profiles table
CREATE INDEX IX_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IX_user_profiles_grade_level ON user_profiles(grade_level);
CREATE INDEX IX_user_profiles_location ON user_profiles(city, province);

-- User sessions for security tracking
CREATE TABLE user_sessions (
    id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    session_token NVARCHAR(255) NOT NULL,
    ip_address NVARCHAR(45),
    user_agent NTEXT,
    expires_at DATETIME2 NOT NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_user_sessions_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT UQ_user_sessions_token UNIQUE (session_token)
);

-- Create indexes for user_sessions table
CREATE INDEX IX_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IX_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IX_user_sessions_expires ON user_sessions(expires_at);

-- =====================================================
-- 2. EDUCATIONAL CONTENT STRUCTURE
-- =====================================================

-- Subject categories (Math, Physics, Chemistry, etc.)
CREATE TABLE subjects (
    id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(100) NOT NULL,
    slug NVARCHAR(100) NOT NULL,
    description NTEXT,
    icon_url NVARCHAR(500),
    color_code NVARCHAR(7), -- Hex color
    sort_order INT DEFAULT 0,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT UQ_subjects_slug UNIQUE (slug)
);

-- Create indexes for subjects table
CREATE INDEX IX_subjects_slug ON subjects(slug);
CREATE INDEX IX_subjects_active ON subjects(is_active);
CREATE INDEX IX_subjects_sort ON subjects(sort_order);

-- Grade levels (Grade 1-12, University, etc.)
CREATE TABLE grade_levels (
    id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(50) NOT NULL,
    slug NVARCHAR(50) NOT NULL,
    description NTEXT,
    sort_order INT DEFAULT 0,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT UQ_grade_levels_slug UNIQUE (slug)
);

-- Create indexes for grade_levels table
CREATE INDEX IX_grade_levels_slug ON grade_levels(slug);
CREATE INDEX IX_grade_levels_sort ON grade_levels(sort_order);

-- Course categories for better organization
CREATE TABLE course_categories (
    id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(100) NOT NULL,
    slug NVARCHAR(100) NOT NULL,
    description NTEXT,
    parent_id INT NULL,
    sort_order INT DEFAULT 0,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_course_categories_parent_id FOREIGN KEY (parent_id) REFERENCES course_categories(id),
    CONSTRAINT UQ_course_categories_slug UNIQUE (slug)
);

-- Create indexes for course_categories table
CREATE INDEX IX_course_categories_slug ON course_categories(slug);
CREATE INDEX IX_course_categories_parent ON course_categories(parent_id);
CREATE INDEX IX_course_categories_sort ON course_categories(sort_order);

-- Main courses table
CREATE TABLE courses (
    id INT IDENTITY(1,1) PRIMARY KEY,
    uuid UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    title NVARCHAR(255) NOT NULL,
    slug NVARCHAR(255) NOT NULL,
    description NTEXT,
    short_description NVARCHAR(500),
    thumbnail_url NVARCHAR(500),
    instructor_id INT NOT NULL,
    subject_id INT NOT NULL,
    grade_level_id INT NOT NULL,
    category_id INT,
    difficulty_level NVARCHAR(20) CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')) DEFAULT 'beginner',
    course_type NVARCHAR(20) CHECK (course_type IN ('free', 'premium', 'subscription')) DEFAULT 'free',
    price DECIMAL(10,2) DEFAULT 0.00,
    duration_hours INT DEFAULT 0,
    max_students INT DEFAULT 0, -- 0 = unlimited
    enrollment_count INT DEFAULT 0,
    rating_average DECIMAL(3,2) DEFAULT 0.00,
    rating_count INT DEFAULT 0,
    status NVARCHAR(20) CHECK (status IN ('draft', 'published', 'archived')) DEFAULT 'draft',
    featured BIT DEFAULT 0,
    tags NVARCHAR(MAX), -- Store tags as JSON string
    learning_objectives NVARCHAR(MAX),
    prerequisites NVARCHAR(MAX),
    published_at DATETIME2,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_courses_instructor_id FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT FK_courses_subject_id FOREIGN KEY (subject_id) REFERENCES subjects(id),
    CONSTRAINT FK_courses_grade_level_id FOREIGN KEY (grade_level_id) REFERENCES grade_levels(id),
    CONSTRAINT FK_courses_category_id FOREIGN KEY (category_id) REFERENCES course_categories(id),
    CONSTRAINT UQ_courses_uuid UNIQUE (uuid),
    CONSTRAINT UQ_courses_slug UNIQUE (slug)
);

-- Create indexes for courses table
CREATE INDEX IX_courses_instructor ON courses(instructor_id);
CREATE INDEX IX_courses_subject ON courses(subject_id);
CREATE INDEX IX_courses_grade ON courses(grade_level_id);
CREATE INDEX IX_courses_category ON courses(category_id);
CREATE INDEX IX_courses_status ON courses(status);
CREATE INDEX IX_courses_featured ON courses(featured);
CREATE INDEX IX_courses_rating ON courses(rating_average DESC);
CREATE INDEX IX_courses_enrollment ON courses(enrollment_count DESC);

-- Create full-text catalog and index for courses search
CREATE FULLTEXT CATALOG ft_catalog AS DEFAULT;
-- Note: Full-text index will be created after table creation is complete

-- Course lessons/chapters
CREATE TABLE lessons (
    id INT IDENTITY(1,1) PRIMARY KEY,
    uuid UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    course_id INT NOT NULL,
    title NVARCHAR(255) NOT NULL,
    slug NVARCHAR(255) NOT NULL,
    description NTEXT,
    content NTEXT,
    lesson_type NVARCHAR(20) CHECK (lesson_type IN ('text', 'video', 'audio', 'interactive', 'quiz')) DEFAULT 'text',
    video_url NVARCHAR(500),
    audio_url NVARCHAR(500),
    duration_minutes INT DEFAULT 0,
    sort_order INT DEFAULT 0,
    is_free BIT DEFAULT 0, -- Free preview lessons
    status NVARCHAR(20) CHECK (status IN ('draft', 'published')) DEFAULT 'draft',
    view_count INT DEFAULT 0,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_lessons_course_id FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    CONSTRAINT UQ_lessons_uuid UNIQUE (uuid),
    CONSTRAINT UQ_lessons_course_slug UNIQUE (course_id, slug)
);

-- Create indexes for lessons table
CREATE INDEX IX_lessons_course ON lessons(course_id);
CREATE INDEX IX_lessons_sort ON lessons(sort_order);
CREATE INDEX IX_lessons_status ON lessons(status);
CREATE INDEX IX_lessons_free ON lessons(is_free);

-- Create full-text index for lessons search
-- Note: Full-text index will be created after table creation is complete

-- Lesson materials (PDFs, images, etc.)
CREATE TABLE lesson_materials (
    id INT IDENTITY(1,1) PRIMARY KEY,
    lesson_id INT NOT NULL,
    title NVARCHAR(255) NOT NULL,
    description NTEXT,
    file_url NVARCHAR(500) NOT NULL,
    file_type NVARCHAR(50),
    file_size INT, -- in bytes
    sort_order INT DEFAULT 0,
    download_count INT DEFAULT 0,
    created_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_lesson_materials_lesson_id FOREIGN KEY (lesson_id) REFERENCES lessons(id) ON DELETE CASCADE
);

-- Create indexes for lesson_materials table
CREATE INDEX IX_lesson_materials_lesson ON lesson_materials(lesson_id);
CREATE INDEX IX_lesson_materials_sort ON lesson_materials(sort_order);

-- =====================================================
-- 3. QUIZ AND ASSESSMENT SYSTEM
-- =====================================================

-- Quiz definitions
CREATE TABLE quizzes (
    id INT IDENTITY(1,1) PRIMARY KEY,
    uuid UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    title NVARCHAR(255) NOT NULL,
    description NTEXT,
    course_id INT,
    lesson_id INT,
    creator_id INT NOT NULL,
    quiz_type NVARCHAR(20) CHECK (quiz_type IN ('practice', 'assessment', 'final_exam')) DEFAULT 'practice',
    difficulty_level NVARCHAR(20) CHECK (difficulty_level IN ('easy', 'medium', 'hard')) DEFAULT 'medium',
    time_limit_minutes INT DEFAULT 0, -- 0 = no limit
    max_attempts INT DEFAULT 0, -- 0 = unlimited
    passing_score DECIMAL(5,2) DEFAULT 70.00,
    randomize_questions BIT DEFAULT 0,
    show_correct_answers BIT DEFAULT 1,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_quizzes_course_id FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    CONSTRAINT FK_quizzes_lesson_id FOREIGN KEY (lesson_id) REFERENCES lessons(id) ON DELETE CASCADE,
    CONSTRAINT FK_quizzes_creator_id FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT UQ_quizzes_uuid UNIQUE (uuid)
);

-- Create indexes for quizzes table
CREATE INDEX IX_quizzes_course ON quizzes(course_id);
CREATE INDEX IX_quizzes_lesson ON quizzes(lesson_id);
CREATE INDEX IX_quizzes_creator ON quizzes(creator_id);
CREATE INDEX IX_quizzes_active ON quizzes(is_active);

-- Quiz questions
CREATE TABLE quiz_questions (
    id INT IDENTITY(1,1) PRIMARY KEY,
    quiz_id INT NOT NULL,
    question_text NTEXT NOT NULL,
    question_type NVARCHAR(20) CHECK (question_type IN ('multiple_choice', 'true_false', 'short_answer', 'essay')) DEFAULT 'multiple_choice',
    points DECIMAL(5,2) DEFAULT 1.00,
    explanation NTEXT,
    sort_order INT DEFAULT 0,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_quiz_questions_quiz_id FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE
);

-- Create indexes for quiz_questions table
CREATE INDEX IX_quiz_questions_quiz ON quiz_questions(quiz_id);
CREATE INDEX IX_quiz_questions_sort ON quiz_questions(sort_order);
CREATE INDEX IX_quiz_questions_active ON quiz_questions(is_active);

-- Quiz question options (for multiple choice)
CREATE TABLE quiz_question_options (
    id INT IDENTITY(1,1) PRIMARY KEY,
    question_id INT NOT NULL,
    option_text NTEXT NOT NULL,
    is_correct BIT DEFAULT 0,
    sort_order INT DEFAULT 0,
    created_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_quiz_question_options_question_id FOREIGN KEY (question_id) REFERENCES quiz_questions(id) ON DELETE CASCADE
);

-- Create indexes for quiz_question_options table
CREATE INDEX IX_quiz_question_options_question ON quiz_question_options(question_id);
CREATE INDEX IX_quiz_question_options_sort ON quiz_question_options(sort_order);

-- Quiz attempts by users
CREATE TABLE quiz_attempts (
    id INT IDENTITY(1,1) PRIMARY KEY,
    quiz_id INT NOT NULL,
    user_id INT NOT NULL,
    attempt_number INT NOT NULL,
    score DECIMAL(5,2) DEFAULT 0.00,
    max_score DECIMAL(5,2) NOT NULL,
    percentage DECIMAL(5,2) DEFAULT 0.00,
    time_taken_minutes INT DEFAULT 0,
    status NVARCHAR(20) CHECK (status IN ('in_progress', 'completed', 'abandoned')) DEFAULT 'in_progress',
    started_at DATETIME2 DEFAULT GETDATE(),
    completed_at DATETIME2 NULL,
    
    CONSTRAINT FK_quiz_attempts_quiz_id FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
    CONSTRAINT FK_quiz_attempts_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT UQ_quiz_attempts_unique UNIQUE (quiz_id, user_id, attempt_number)
);

-- Create indexes for quiz_attempts table
CREATE INDEX IX_quiz_attempts_quiz ON quiz_attempts(quiz_id);
CREATE INDEX IX_quiz_attempts_user ON quiz_attempts(user_id);
CREATE INDEX IX_quiz_attempts_status ON quiz_attempts(status);
CREATE INDEX IX_quiz_attempts_score ON quiz_attempts(score DESC);

-- User answers to quiz questions
CREATE TABLE quiz_answers (
    id INT IDENTITY(1,1) PRIMARY KEY,
    attempt_id INT NOT NULL,
    question_id INT NOT NULL,
    selected_option_id INT,
    answer_text NTEXT,
    is_correct BIT DEFAULT 0,
    points_earned DECIMAL(5,2) DEFAULT 0.00,
    ai_feedback NTEXT, -- AI-generated feedback
    created_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_quiz_answers_attempt_id FOREIGN KEY (attempt_id) REFERENCES quiz_attempts(id) ON DELETE CASCADE,
    CONSTRAINT FK_quiz_answers_question_id FOREIGN KEY (question_id) REFERENCES quiz_questions(id) ON DELETE CASCADE,
    CONSTRAINT FK_quiz_answers_selected_option_id FOREIGN KEY (selected_option_id) REFERENCES quiz_question_options(id),
    CONSTRAINT UQ_quiz_answers_unique UNIQUE (attempt_id, question_id)
);

-- Create indexes for quiz_answers table
CREATE INDEX IX_quiz_answers_attempt ON quiz_answers(attempt_id);
CREATE INDEX IX_quiz_answers_question ON quiz_answers(question_id);

-- =====================================================
-- 4. FORUM AND COMMUNITY FEATURES
-- =====================================================

-- Forum categories
CREATE TABLE forum_categories (
    id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(100) NOT NULL,
    slug NVARCHAR(100) NOT NULL,
    description NTEXT,
    icon NVARCHAR(50),
    color_code NVARCHAR(7),
    parent_id INT,
    sort_order INT DEFAULT 0,
    post_count INT DEFAULT 0,
    last_post_id INT,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_forum_categories_parent_id FOREIGN KEY (parent_id) REFERENCES forum_categories(id),
    CONSTRAINT UQ_forum_categories_slug UNIQUE (slug)
);

-- Create indexes for forum_categories table
CREATE INDEX IX_forum_categories_slug ON forum_categories(slug);
CREATE INDEX IX_forum_categories_parent ON forum_categories(parent_id);
CREATE INDEX IX_forum_categories_sort ON forum_categories(sort_order);
CREATE INDEX IX_forum_categories_active ON forum_categories(is_active);

-- Forum posts/discussions
CREATE TABLE forum_posts (
    id INT IDENTITY(1,1) PRIMARY KEY,
    uuid UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
    title NVARCHAR(255) NOT NULL,
    slug NVARCHAR(255) NOT NULL,
    content NTEXT NOT NULL,
    author_id INT NOT NULL,
    category_id INT NOT NULL,
    post_type NVARCHAR(20) CHECK (post_type IN ('discussion', 'question', 'announcement')) DEFAULT 'discussion',
    status NVARCHAR(20) CHECK (status IN ('published', 'pending', 'locked', 'deleted')) DEFAULT 'published',
    is_pinned BIT DEFAULT 0,
    is_featured BIT DEFAULT 0,
    view_count INT DEFAULT 0,
    reply_count INT DEFAULT 0,
    like_count INT DEFAULT 0,
    dislike_count INT DEFAULT 0,
    last_reply_at DATETIME2 NULL,
    last_reply_user_id INT,
    tags NVARCHAR(MAX),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_forum_posts_author_id FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT FK_forum_posts_category_id FOREIGN KEY (category_id) REFERENCES forum_categories(id),
    CONSTRAINT FK_forum_posts_last_reply_user_id FOREIGN KEY (last_reply_user_id) REFERENCES users(id),
    CONSTRAINT UQ_forum_posts_uuid UNIQUE (uuid),
    CONSTRAINT UQ_forum_posts_category_slug UNIQUE (category_id, slug)
);

-- Create indexes for forum_posts table
CREATE INDEX IX_forum_posts_author ON forum_posts(author_id);
CREATE INDEX IX_forum_posts_category ON forum_posts(category_id);
CREATE INDEX IX_forum_posts_status ON forum_posts(status);
CREATE INDEX IX_forum_posts_pinned ON forum_posts(is_pinned);
CREATE INDEX IX_forum_posts_featured ON forum_posts(is_featured);
CREATE INDEX IX_forum_posts_last_reply ON forum_posts(last_reply_at DESC);
CREATE INDEX IX_forum_posts_views ON forum_posts(view_count DESC);
CREATE INDEX IX_forum_posts_likes ON forum_posts(like_count DESC);

-- Create full-text index for forum_posts search
-- Note: Full-text index will be created after table creation is complete

-- Forum post replies
CREATE TABLE forum_replies (
    id INT IDENTITY(1,1) PRIMARY KEY,
    post_id INT NOT NULL,
    parent_reply_id INT, -- For nested replies
    author_id INT NOT NULL,
    content NTEXT NOT NULL,
    status NVARCHAR(20) CHECK (status IN ('published', 'pending', 'deleted')) DEFAULT 'published',
    like_count INT DEFAULT 0,
    dislike_count INT DEFAULT 0,
    is_solution BIT DEFAULT 0, -- Mark as solution for questions
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_forum_replies_post_id FOREIGN KEY (post_id) REFERENCES forum_posts(id) ON DELETE CASCADE,
    CONSTRAINT FK_forum_replies_parent_reply_id FOREIGN KEY (parent_reply_id) REFERENCES forum_replies(id),
    CONSTRAINT FK_forum_replies_author_id FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for forum_replies table
CREATE INDEX IX_forum_replies_post ON forum_replies(post_id);
CREATE INDEX IX_forum_replies_parent ON forum_replies(parent_reply_id);
CREATE INDEX IX_forum_replies_author ON forum_replies(author_id);
CREATE INDEX IX_forum_replies_status ON forum_replies(status);
CREATE INDEX IX_forum_replies_solution ON forum_replies(is_solution);
CREATE INDEX IX_forum_replies_created ON forum_replies(created_at);

-- Create full-text index for forum_replies search
-- Note: Full-text index will be created after table creation is complete

-- Post and reply reactions (likes, dislikes, etc.)
CREATE TABLE post_reactions (
    id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    post_id INT,
    reply_id INT,
    reaction_type NVARCHAR(20) CHECK (reaction_type IN ('like', 'dislike', 'love', 'helpful', 'funny')) NOT NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_post_reactions_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT FK_post_reactions_post_id FOREIGN KEY (post_id) REFERENCES forum_posts(id) ON DELETE CASCADE,
    CONSTRAINT FK_post_reactions_reply_id FOREIGN KEY (reply_id) REFERENCES forum_replies(id) ON DELETE CASCADE
);

-- Create indexes for post_reactions table
CREATE INDEX IX_post_reactions_user ON post_reactions(user_id);
CREATE INDEX IX_post_reactions_post ON post_reactions(post_id);
CREATE INDEX IX_post_reactions_reply ON post_reactions(reply_id);
CREATE INDEX IX_post_reactions_type ON post_reactions(reaction_type);

-- User bookmarks
CREATE TABLE bookmarks (
    id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    post_id INT,
    course_id INT,
    lesson_id INT,
    bookmark_type NVARCHAR(20) CHECK (bookmark_type IN ('post', 'course', 'lesson')) NOT NULL,
    notes NTEXT,
    created_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_bookmarks_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT FK_bookmarks_post_id FOREIGN KEY (post_id) REFERENCES forum_posts(id) ON DELETE CASCADE,
    CONSTRAINT FK_bookmarks_course_id FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    CONSTRAINT FK_bookmarks_lesson_id FOREIGN KEY (lesson_id) REFERENCES lessons(id) ON DELETE CASCADE
);

-- Create indexes for bookmarks table
CREATE INDEX IX_bookmarks_user ON bookmarks(user_id);
CREATE INDEX IX_bookmarks_type ON bookmarks(bookmark_type);
CREATE INDEX IX_bookmarks_created ON bookmarks(created_at DESC);

-- =====================================================
-- 5. ENROLLMENT AND PROGRESS TRACKING
-- =====================================================

-- Course enrollments
CREATE TABLE course_enrollments (
    id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    enrollment_type NVARCHAR(20) CHECK (enrollment_type IN ('free', 'paid', 'scholarship')) DEFAULT 'free',
    status NVARCHAR(20) CHECK (status IN ('active', 'completed', 'dropped', 'suspended')) DEFAULT 'active',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    last_accessed_lesson_id INT,
    completion_date DATETIME2,
    certificate_issued BIT DEFAULT 0,
    certificate_url NVARCHAR(500),
    enrolled_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_course_enrollments_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT FK_course_enrollments_course_id FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    CONSTRAINT FK_course_enrollments_last_accessed_lesson_id FOREIGN KEY (last_accessed_lesson_id) REFERENCES lessons(id),
    CONSTRAINT UQ_course_enrollments_unique UNIQUE (user_id, course_id)
);

-- Create indexes for course_enrollments table
CREATE INDEX IX_course_enrollments_user ON course_enrollments(user_id);
CREATE INDEX IX_course_enrollments_course ON course_enrollments(course_id);
CREATE INDEX IX_course_enrollments_status ON course_enrollments(status);
CREATE INDEX IX_course_enrollments_progress ON course_enrollments(progress_percentage DESC);
CREATE INDEX IX_course_enrollments_enrolled ON course_enrollments(enrolled_at DESC);

-- Lesson progress tracking
CREATE TABLE lesson_progress (
    id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    lesson_id INT NOT NULL,
    course_id INT NOT NULL,
    status NVARCHAR(20) CHECK (status IN ('not_started', 'in_progress', 'completed')) DEFAULT 'not_started',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    time_spent_minutes INT DEFAULT 0,
    last_position NVARCHAR(50), -- For video/audio position
    completed_at DATETIME2,
    first_accessed_at DATETIME2 DEFAULT GETDATE(),
    last_accessed_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT FK_lesson_progress_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT FK_lesson_progress_lesson_id FOREIGN KEY (lesson_id) REFERENCES lessons(id) ON DELETE CASCADE,
    CONSTRAINT FK_lesson_progress_course_id FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    CONSTRAINT UQ_lesson_progress_unique UNIQUE (user_id, lesson_id)
);

-- Create indexes for lesson_progress table
CREATE INDEX IX_lesson_progress_user ON lesson_progress(user_id);
CREATE INDEX IX_lesson_progress_lesson ON lesson_progress(lesson_id);
CREATE INDEX IX_lesson_progress_course ON lesson_progress(course_id);
CREATE INDEX IX_lesson_progress_status ON lesson_progress(status);
CREATE INDEX IX_lesson_progress_completed ON lesson_progress(completed_at DESC);

-- =====================================================
-- 6. AI-ASSISTED FEATURES
-- =====================================================

-- AI-generated content summaries
CREATE TABLE ai_content_summaries (
    id INT IDENTITY(1,1) PRIMARY KEY,
    content_type NVARCHAR(20) CHECK (content_type IN ('lesson', 'post', 'course')) NOT NULL,
    content_id INT NOT NULL,
    summary_text NTEXT NOT NULL,
    key_points NVARCHAR(MAX),
    difficulty_assessment NVARCHAR(20) CHECK (difficulty_assessment IN ('easy', 'medium', 'hard')),
    estimated_read_time INT, -- in minutes
    ai_model_version NVARCHAR(50),
    confidence_score DECIMAL(3,2),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    
    CONSTRAINT UQ_ai_content_summaries_unique UNIQUE (content_type, content_id)
);

-- Create indexes for ai_content_summaries table
CREATE INDEX IX_ai_content_summaries_content_type ON ai_content_summaries(content_type);
CREATE INDEX IX_ai_content_summaries_confidence ON ai_content_summaries(confidence_score DESC);
CREATE INDEX IX_ai_content_summaries_created ON ai_content_summaries(created_at DESC);

-- Create triggers for updating timestamps
GO
CREATE TRIGGER tr_users_update_timestamp
ON users
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE users 
    SET updated_at = GETDATE()
    FROM users u
    INNER JOIN inserted i ON u.id = i.id;
END;
GO

CREATE TRIGGER tr_user_profiles_update_timestamp
ON user_profiles
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE user_profiles 
    SET updated_at = GETDATE()
    FROM user_profiles up
    INNER JOIN inserted i ON up.id = i.id;
END;
GO

-- Insert sample data
INSERT INTO subjects (name, slug, description, sort_order) VALUES
(N'Toán học', 'toan-hoc', N'Môn Toán từ cơ bản đến nâng cao', 1),
(N'Vật lý', 'vat-ly', N'Môn Vật lý các cấp', 2),
(N'Hóa học', 'hoa-hoc', N'Môn Hóa học từ THCS đến THPT', 3),
(N'Tiếng Anh', 'tieng-anh', N'Học tiếng Anh giao tiếp và học thuật', 4),
(N'Ngữ văn', 'ngu-van', N'Môn Ngữ văn Việt Nam', 5);

INSERT INTO grade_levels (name, slug, sort_order) VALUES
(N'Lớp 6', 'lop-6', 6),
(N'Lớp 7', 'lop-7', 7),
(N'Lớp 8', 'lop-8', 8),
(N'Lớp 9', 'lop-9', 9),
(N'Lớp 10', 'lop-10', 10),
(N'Lớp 11', 'lop-11', 11),
(N'Lớp 12', 'lop-12', 12);

INSERT INTO forum_categories (name, slug, description, sort_order) VALUES
(N'Thảo luận chung', 'thao-luan-chung', N'Nơi thảo luận các chủ đề tổng quát', 1),
(N'Hỏi đáp học tập', 'hoi-dap-hoc-tap', N'Đặt câu hỏi và nhận trợ giúp học tập', 2),
(N'Chia sẻ kinh nghiệm', 'chia-se-kinh-nghiem', N'Chia sẻ phương pháp học tập hiệu quả', 3),
(N'Thông báo', 'thong-bao', N'Thông báo từ ban quản trị', 4);

-- =====================================================
-- CREATE FULL-TEXT INDEXES (After all tables are created)
-- =====================================================

-- Create unique indexes for full-text search keys
CREATE UNIQUE INDEX IX_courses_id_unique ON courses(id);
CREATE UNIQUE INDEX IX_lessons_id_unique ON lessons(id);
CREATE UNIQUE INDEX IX_forum_posts_id_unique ON forum_posts(id);
CREATE UNIQUE INDEX IX_forum_replies_id_unique ON forum_replies(id);

-- Full-text index for courses search
IF EXISTS (SELECT * FROM sys.fulltext_catalogs WHERE name = 'ft_catalog')
BEGIN
    CREATE FULLTEXT INDEX ON courses(title, description, short_description)
    KEY INDEX IX_courses_id_unique;
END

-- Full-text index for lessons search
IF EXISTS (SELECT * FROM sys.fulltext_catalogs WHERE name = 'ft_catalog')
BEGIN
    CREATE FULLTEXT INDEX ON lessons(title, description, content)
    KEY INDEX IX_lessons_id_unique;
END

-- Full-text index for forum_posts search
IF EXISTS (SELECT * FROM sys.fulltext_catalogs WHERE name = 'ft_catalog')
BEGIN
    CREATE FULLTEXT INDEX ON forum_posts(title, content)
    KEY INDEX IX_forum_posts_id_unique;
END

-- Full-text index for forum_replies search
IF EXISTS (SELECT * FROM sys.fulltext_catalogs WHERE name = 'ft_catalog')
BEGIN
    CREATE FULLTEXT INDEX ON forum_replies(content)
    KEY INDEX IX_forum_replies_id_unique;
END

GO